{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.alwan.kids2025.app-mergeDebugResources-56:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,36,37,38,39,40,41,42,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "687,762,873,962,1063,1170,1277,1376,1483,1586,1713,1801,1925,2027,2129,2245,2347,2461,2589,2705,2827,2963,3083,3217,3337,3449,3664,3781,3905,4035,4157,4295,4429,4545", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "757,868,957,1058,1165,1272,1371,1478,1581,1708,1796,1920,2022,2124,2240,2342,2456,2584,2700,2822,2958,3078,3212,3332,3444,3570,3776,3900,4030,4152,4290,4424,4540,4660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,35", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,3575", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,3659"}}]}, {"outputFile": "com.alwan.kids2025.app-mergeDebugResources-56:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4912,4987,5098,5187,5288,5395,5502,5601,5708,5811,5938,6026,6150,6252,6354,6470,6572,6686,6814,6930,7052,7188,7308,7442,7562,7674,7889,8006,8130,8260,8382,8520,8654,8770", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "4982,5093,5182,5283,5390,5497,5596,5703,5806,5933,6021,6145,6247,6349,6465,6567,6681,6809,6925,7047,7183,7303,7437,7557,7669,7795,8001,8125,8255,8377,8515,8649,8765,8885"}}, {"source": "Z:\\alwan5\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "3,21,14,25,68,58,30,50,38", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "86,1039,742,1204,3093,2589,1406,2261,1725", "endLines": "12,23,19,27,73,65,35,55,48", "endColumns": "12,12,12,12,12,12,12,12,12", "endOffsets": "736,1198,1033,1352,3419,3039,1671,2533,2255"}, "to": {"startLines": "24,34,37,43,46,52,60,66,72", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1144,1799,1963,2259,2412,2743,3198,3468,3745", "endLines": "33,36,42,45,51,59,65,71,82", "endColumns": "12,12,12,12,12,12,12,12,12", "endOffsets": "1794,1958,2254,2407,2738,3193,3463,3740,4275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "83,84,85,86,87,88,89,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4280,4350,4434,4518,4614,4716,4818,7800", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "4345,4429,4513,4609,4711,4813,4907,7884"}}, {"source": "Z:\\alwan5\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "8,31,32,10,5,3,4,20,21,23,18,19,22,26,27,28,36,35,9,13,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "281,1210,1259,368,184,86,133,757,805,906,657,706,857,1006,1055,1107,1416,1365,326,463,509,559", "endColumns": "44,48,50,49,45,46,50,47,51,48,48,50,48,48,51,55,54,50,41,45,49,50", "endOffsets": "321,1254,1305,413,225,128,179,800,852,950,701,752,901,1050,1102,1158,1466,1411,363,504,554,605"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,100,149,200,250,296,343,394,442,494,543,592,643,692,741,793,849,904,955,997,1043,1093", "endColumns": "44,48,50,49,45,46,50,47,51,48,48,50,48,48,51,55,54,50,41,45,49,50", "endOffsets": "95,144,195,245,291,338,389,437,489,538,587,638,687,736,788,844,899,950,992,1038,1088,1139"}}]}]}