package com.alwan.kids2025;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.LinearLayout;

public class GalleryActivity extends BaseActivity {

    private CardView emptyStateCard;
    private CardView btnStartColoring;
    private RecyclerView galleryRecyclerView;
    private LinearLayout galleryActions;
    private CardView btnClearAll;
    private CardView btnShareAll;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_gallery);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(getString(R.string.gallery_title));
        }

        // Setup navigation drawer
        setUpNavView();

        // Initialize views
        initViews();

        // Setup listeners
        setupListeners();

        // Load gallery data
        loadGalleryData();
    }

    private void initViews() {
        emptyStateCard = findViewById(R.id.empty_state_card);
        btnStartColoring = findViewById(R.id.btn_start_coloring);
        galleryRecyclerView = findViewById(R.id.gallery_recycler_view);
        galleryActions = findViewById(R.id.gallery_actions);
        btnClearAll = findViewById(R.id.btn_clear_all);
        btnShareAll = findViewById(R.id.btn_share_all);
    }

    private void setupListeners() {
        btnStartColoring.setOnClickListener(v -> {
            Intent intent = new Intent(this, Categories.class);
            startActivity(intent);
        });

        btnClearAll.setOnClickListener(v -> {
            // TODO: Implement clear all functionality
            showEmptyState();
        });

        btnShareAll.setOnClickListener(v -> {
            // TODO: Implement share all functionality
        });
    }

    private void loadGalleryData() {
        // TODO: Load saved artworks from storage
        // For now, show empty state
        showEmptyState();
    }

    private void showEmptyState() {
        emptyStateCard.setVisibility(View.VISIBLE);
        galleryRecyclerView.setVisibility(View.GONE);
        galleryActions.setVisibility(View.GONE);
    }

    private void showGalleryContent() {
        emptyStateCard.setVisibility(View.GONE);
        galleryRecyclerView.setVisibility(View.VISIBLE);
        galleryActions.setVisibility(View.VISIBLE);
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    @Override
    protected boolean useDrawerToggle() {
        return false;
    }
}
