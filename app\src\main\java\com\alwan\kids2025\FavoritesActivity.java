package com.alwan.kids2025;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.LinearLayout;

public class FavoritesActivity extends BaseActivity {

    private CardView emptyFavoritesCard;
    private CardView btnBrowseCategories;
    private RecyclerView favoritesRecyclerView;
    private LinearLayout favoritesActions;
    private CardView btnClearFavorites;
    private CardView btnExportFavorites;
    private FavoritesManager favoritesManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_favorites);

        // Initialize favorites manager
        favoritesManager = new FavoritesManager(this);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(getString(R.string.favorites_title));
        }

        // Handle back navigation
        getOnBackPressedDispatcher().addCallback(this, new androidx.activity.OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                finish();
            }
        });

        // Initialize views
        initViews();

        // Setup listeners
        setupListeners();

        // Setup bottom navigation
        setupBottomNavigation();

        // Load favorites data
        loadFavoritesData();
    }

    private void initViews() {
        emptyFavoritesCard = findViewById(R.id.empty_favorites_card);
        btnBrowseCategories = findViewById(R.id.btn_browse_categories);
        favoritesRecyclerView = findViewById(R.id.favorites_recycler_view);
        favoritesActions = findViewById(R.id.favorites_actions);
        btnClearFavorites = findViewById(R.id.btn_clear_favorites);
        btnExportFavorites = findViewById(R.id.btn_export_favorites);
    }

    private void setupListeners() {
        btnBrowseCategories.setOnClickListener(v -> {
            Intent intent = new Intent(this, Categories.class);
            startActivity(intent);
        });

        btnClearFavorites.setOnClickListener(v -> {
            clearAllFavorites();
        });

        btnExportFavorites.setOnClickListener(v -> {
            exportFavorites();
        });
    }

    private void loadFavoritesData() {
        if (favoritesManager.getFavoritesCount() > 0) {
            showFavoritesContent();
        } else {
            showEmptyState();
        }
    }

    private void clearAllFavorites() {
        favoritesManager.clearAllFavorites();
        showEmptyState();
    }

    private void exportFavorites() {
        // TODO: Implement export functionality
    }

    private void showEmptyState() {
        emptyFavoritesCard.setVisibility(View.VISIBLE);
        favoritesRecyclerView.setVisibility(View.GONE);
        favoritesActions.setVisibility(View.GONE);
    }

    private void showFavoritesContent() {
        emptyFavoritesCard.setVisibility(View.GONE);
        favoritesRecyclerView.setVisibility(View.VISIBLE);
        favoritesActions.setVisibility(View.VISIBLE);
    }

    private void setupBottomNavigation() {
        LinearLayout navHome = findViewById(R.id.nav_home);
        LinearLayout navCategories = findViewById(R.id.nav_categories);
        LinearLayout navGallery = findViewById(R.id.nav_gallery);
        LinearLayout navFavorites = findViewById(R.id.nav_favorites);
        LinearLayout navMore = findViewById(R.id.nav_more);

        // Set Favorites as active
        if (navFavorites != null) {
            findViewById(R.id.nav_favorites_icon).setBackgroundColor(getResources().getColor(R.color.favorites_color));
            ((android.widget.TextView) findViewById(R.id.nav_favorites_text)).setTextColor(getResources().getColor(R.color.favorites_color));
        }

        // Home navigation
        if (navHome != null) {
            navHome.setOnClickListener(v -> {
                Intent intent = new Intent(this, Categories.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
                finish();
            });
        }

        // Categories navigation
        if (navCategories != null) {
            navCategories.setOnClickListener(v -> {
                Intent intent = new Intent(this, Categories.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
                finish();
            });
        }

        // Gallery navigation
        if (navGallery != null) {
            navGallery.setOnClickListener(v -> {
                Intent intent = new Intent(this, GalleryActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
                finish();
            });
        }

        // Favorites navigation
        if (navFavorites != null) {
            navFavorites.setOnClickListener(v -> {
                // Already on favorites page
            });
        }

        // More navigation
        if (navMore != null) {
            navMore.setOnClickListener(v -> {
                Intent intent = new Intent(this, MoreActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
                finish();
            });
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    @Override
    protected boolean useDrawerToggle() {
        return false;
    }
}
