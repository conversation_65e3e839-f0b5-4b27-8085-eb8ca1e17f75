<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/tools/ns/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#E3F2FD" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="#E8F5E8" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="16dp" />
        </shape>
    </item>
</selector>
