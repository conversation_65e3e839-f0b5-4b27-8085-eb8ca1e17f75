<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:padding="32dp"
    android:background="@color/dialog_background">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialog_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        app:cardCornerRadius="24dp"
        app:cardElevation="16dp"
        app:cardBackgroundColor="@android:color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Icon -->
            <androidx.cardview.widget.CardView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="40dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/modern_accent_light">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_exit_app"
                    android:tint="@color/modern_accent" />

            </androidx.cardview.widget.CardView>

            <!-- Title -->
            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="تأكيد الخروج"
                android:textColor="@color/text_primary"
                android:textSize="22sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo"
                android:layout_marginBottom="12dp" />

            <!-- Message -->
            <TextView
                android:id="@+id/dialog_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="هل أنت متأكد من أنك تريد الخروج من التطبيق؟\nسيتم حفظ تقدمك تلقائياً."
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:fontFamily="@font/blabeloo"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="24dp" />

            <!-- Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- Cancel Button -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:foreground="@drawable/beautiful_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/light_gray">

                    <Button
                        android:id="@+id/btn_cancel"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:text="إلغاء"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:background="@android:color/transparent"
                        android:gravity="center" />

                </androidx.cardview.widget.CardView>

                <!-- Exit Button -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:foreground="@drawable/beautiful_ripple_effect"
                    android:clickable="true"
                    android:focusable="true"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp"
                    app:cardBackgroundColor="@color/modern_accent">

                    <Button
                        android:id="@+id/btn_exit"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:text="خروج"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/blabeloo"
                        android:background="@android:color/transparent"
                        android:gravity="center" />

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
