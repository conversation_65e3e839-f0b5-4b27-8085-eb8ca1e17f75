package com.alwan.kids2025;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.graphics.drawable.ColorDrawable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.preference.PreferenceManager;
import java.util.Locale;

public class SettingsHelper {
    
    public static void applyLanguageAndTheme(Activity activity) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(activity);
        
        // Apply language
        String savedLanguage = preferences.getString("app_language", "ar");
        applyLanguage(activity, savedLanguage);
        
        // Apply dark mode
        boolean isDarkMode = preferences.getBoolean("dark_mode_enabled", false);
        applyDarkMode(activity, isDarkMode);
    }
    
    public static void applyLanguage(Context context, String languageCode) {
        try {
            Locale locale;
            if (languageCode.equals("ar")) {
                locale = new Locale("ar");
            } else {
                locale = new Locale("en");
            }
            
            Locale.setDefault(locale);
            Configuration config = new Configuration();
            config.locale = locale;
            context.getResources().updateConfiguration(config,
                context.getResources().getDisplayMetrics());
                
        } catch (Exception e) {
            // Handle error silently
        }
    }
    
    public static void applyDarkMode(Activity activity, boolean isDarkMode) {
        try {
            if (isDarkMode) {
                // Apply dark theme colors to current activity
                activity.getWindow().getDecorView().setBackgroundColor(0xFF121212); // Dark gray
                activity.getWindow().setStatusBarColor(0xFF000000); // Black status bar
                
                // Apply to toolbar if exists
                if (activity instanceof AppCompatActivity) {
                    AppCompatActivity appCompatActivity = (AppCompatActivity) activity;
                    if (appCompatActivity.getSupportActionBar() != null) {
                        appCompatActivity.getSupportActionBar().setBackgroundDrawable(
                            new ColorDrawable(0xFF1F1F1F));
                    }
                }
                
                // Set global dark mode
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
            } else {
                // Apply light theme colors
                activity.getWindow().getDecorView().setBackgroundColor(0xFFFFFFFF); // White
                activity.getWindow().setStatusBarColor(0xFF6200EE); // Primary color
                
                // Apply to toolbar if exists
                if (activity instanceof AppCompatActivity) {
                    AppCompatActivity appCompatActivity = (AppCompatActivity) activity;
                    if (appCompatActivity.getSupportActionBar() != null) {
                        appCompatActivity.getSupportActionBar().setBackgroundDrawable(
                            new ColorDrawable(0xFF6200EE));
                    }
                }
                
                // Set global light mode
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
            }
        } catch (Exception e) {
            // Handle error silently
        }
    }
    
    public static void changeLanguage(Activity activity, String languageCode) {
        try {
            // Save language preference
            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(activity);
            preferences.edit().putString("app_language", languageCode).apply();
            
            // Apply language change
            applyLanguage(activity, languageCode);
            
            // Restart activity to apply changes completely
            activity.recreate();
            
        } catch (Exception e) {
            // Handle error silently
        }
    }
    
    public static void changeDarkMode(Activity activity, boolean isDarkMode) {
        try {
            // Save dark mode preference
            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(activity);
            preferences.edit().putBoolean("dark_mode_enabled", isDarkMode).apply();
            
            // Apply dark mode change
            applyDarkMode(activity, isDarkMode);
            
        } catch (Exception e) {
            // Handle error silently
        }
    }
}
