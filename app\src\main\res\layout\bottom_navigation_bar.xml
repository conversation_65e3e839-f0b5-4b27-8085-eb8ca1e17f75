<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="90dp"
    android:layout_gravity="bottom"
    app:cardElevation="24dp"
    app:cardCornerRadius="28dp"
    app:cardUseCompatPadding="true"
    android:layout_margin="12dp"
    app:cardBackgroundColor="@color/nav_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:background="@android:color/transparent"
        android:paddingTop="14dp"
        android:paddingBottom="14dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp">

        <!-- Home Tab -->
        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/nav_item_ripple"
            android:clickable="true"
            android:focusable="true"
            android:padding="6dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/nav_home_icon"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="22dp"
                app:cardElevation="12dp"
                app:cardBackgroundColor="@color/nav_item_unselected">

                <ImageView
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_home_modern"
                    android:tint="@android:color/white" />

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/nav_home_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/home"
                android:textColor="@color/nav_item_unselected"
                android:textSize="12sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo" />

        </LinearLayout>

        <!-- Categories Tab -->
        <LinearLayout
            android:id="@+id/nav_categories"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/nav_item_ripple"
            android:clickable="true"
            android:focusable="true"
            android:padding="6dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/nav_categories_icon"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="22dp"
                app:cardElevation="12dp"
                app:cardBackgroundColor="@color/nav_item_unselected">

                <ImageView
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_categories_modern"
                    android:tint="@android:color/white" />

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/nav_categories_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/categories"
                android:textColor="@color/nav_item_unselected"
                android:textSize="12sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo" />

        </LinearLayout>

        <!-- Gallery Tab -->
        <LinearLayout
            android:id="@+id/nav_gallery"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/nav_item_ripple"
            android:clickable="true"
            android:focusable="true"
            android:padding="6dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/nav_gallery_icon"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="22dp"
                app:cardElevation="12dp"
                app:cardBackgroundColor="@color/nav_item_unselected">

                <ImageView
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_gallery_modern"
                    android:tint="@android:color/white" />

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/nav_gallery_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/gallery"
                android:textColor="@color/nav_item_unselected"
                android:textSize="12sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo" />

        </LinearLayout>

        <!-- Favorites Tab -->
        <LinearLayout
            android:id="@+id/nav_favorites"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/nav_item_ripple"
            android:clickable="true"
            android:focusable="true"
            android:padding="6dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/nav_favorites_icon"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="22dp"
                app:cardElevation="12dp"
                app:cardBackgroundColor="@color/nav_item_unselected">

                <ImageView
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_favorites_modern"
                    android:tint="@android:color/white" />

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/nav_favorites_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/favorites"
                android:textColor="@color/nav_item_unselected"
                android:textSize="12sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo" />

        </LinearLayout>

        <!-- More Tab -->
        <LinearLayout
            android:id="@+id/nav_more"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/nav_item_ripple"
            android:clickable="true"
            android:focusable="true"
            android:padding="6dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/nav_more_icon"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginBottom="8dp"
                app:cardCornerRadius="22dp"
                app:cardElevation="12dp"
                app:cardBackgroundColor="@color/nav_item_unselected">

                <ImageView
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_more_modern"
                    android:tint="@android:color/white" />

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/nav_more_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/more"
                android:textColor="@color/nav_item_unselected"
                android:textSize="12sp"
                android:textStyle="bold"
                android:fontFamily="@font/blabeloo" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
