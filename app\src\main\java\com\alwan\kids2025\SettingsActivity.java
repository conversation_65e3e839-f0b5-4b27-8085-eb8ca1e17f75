package com.alwan.kids2025;

import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Bundle;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.preference.PreferenceManager;

public class SettingsActivity extends BaseActivity {

    private SwitchCompat switchSoundEffects;
    private SwitchCompat switchBackgroundMusic;
    private SwitchCompat switchAutoSave;
    private SwitchCompat switchShowGrid;
    private SwitchCompat switchDarkMode;
    private LinearLayout btnPrivacyPolicy;
    private LinearLayout btnResetAds;
    private CardView languageCard;
    private CardView qualityCard;
    private TextView languageText;
    private TextView qualityText;

    private SharedPreferences preferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        // Setup toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setTitle(getString(R.string.settings_title));
        }

        // Setup navigation drawer
        setUpNavView();

        // Initialize preferences - use same preferences as other activities
        preferences = PreferenceManager.getDefaultSharedPreferences(this);

        // Apply saved language and theme settings
        SettingsHelper.applyLanguageAndTheme(this);

        // Initialize views
        initViews();

        // Load settings
        loadSettings();

        // Setup listeners
        setupListeners();
    }

    private void initViews() {
        switchSoundEffects = findViewById(R.id.switch_sound_effects);
        switchBackgroundMusic = findViewById(R.id.switch_background_music);
        switchAutoSave = findViewById(R.id.switch_auto_save);
        switchShowGrid = findViewById(R.id.switch_show_grid);
        switchDarkMode = findViewById(R.id.switch_dark_mode);
        btnPrivacyPolicy = findViewById(R.id.btn_privacy_policy);
        btnResetAds = findViewById(R.id.btn_reset_ads);

        // Language and quality elements
        languageCard = findViewById(R.id.language_card);
        qualityCard = findViewById(R.id.quality_card);
        languageText = findViewById(R.id.language_text);
        qualityText = findViewById(R.id.quality_text);
    }

    private void loadSettings() {
        switchSoundEffects.setChecked(preferences.getBoolean("sound_effects", true));
        switchBackgroundMusic.setChecked(preferences.getBoolean("background_music", true));
        switchAutoSave.setChecked(preferences.getBoolean("auto_save", true));
        switchShowGrid.setChecked(preferences.getBoolean("show_grid", false));
        switchDarkMode.setChecked(preferences.getBoolean("dark_mode_enabled", false));

        // Load language setting
        String language = preferences.getString("app_language", "ar");
        languageText.setText(language.equals("ar") ? getString(R.string.arabic_language) : getString(R.string.english_language));

        // Load quality setting
        String quality = preferences.getString("image_quality", "high");
        qualityText.setText(getQualityText(quality));

        // Apply current dark mode setting
        boolean isDarkMode = preferences.getBoolean("dark_mode_enabled", false);
        if (isDarkMode) {
            applyDarkMode(true);
        }
    }

    private void setupListeners() {
        switchSoundEffects.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                preferences.edit().putBoolean("sound_effects", isChecked).apply();
                showToast(isChecked ? getString(R.string.sound_effects_enabled) : getString(R.string.sound_effects_disabled));
            }
        });

        switchBackgroundMusic.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                preferences.edit().putBoolean("background_music", isChecked).apply();
                showToast(isChecked ? getString(R.string.background_music_enabled) : getString(R.string.background_music_disabled));
            }
        });

        switchAutoSave.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                preferences.edit().putBoolean("auto_save", isChecked).apply();
                showToast(isChecked ? getString(R.string.auto_save_enabled) : getString(R.string.auto_save_disabled));
            }
        });

        switchShowGrid.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                preferences.edit().putBoolean("show_grid", isChecked).apply();
                showToast(isChecked ? getString(R.string.grid_enabled) : getString(R.string.grid_disabled));
            }
        });

        switchDarkMode.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                showToast(isChecked ? getString(R.string.dark_mode_enabled) : getString(R.string.dark_mode_disabled));
                SettingsHelper.changeDarkMode(SettingsActivity.this, isChecked);
            }
        });

        // Set listeners for language and quality cards
        languageCard.setOnClickListener(v -> showLanguageDialog());
        qualityCard.setOnClickListener(v -> showQualityDialog());

        btnPrivacyPolicy.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW,
                Uri.parse("https://sites.google.com/view/colors-kids"));
            startActivity(intent);
        });

        btnResetAds.setOnClickListener(v -> {
            adsSettings();
        });
    }

    private void showLanguageDialog() {
        String[] languages = {getString(R.string.arabic_language), getString(R.string.english_language)};
        String[] languageCodes = {"ar", "en"};

        String currentLanguage = preferences.getString("app_language", "ar");
        int selectedIndex = currentLanguage.equals("ar") ? 0 : 1;

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.choose_language));
        builder.setSingleChoiceItems(languages, selectedIndex, (dialog, which) -> {
            String selectedLanguage = languageCodes[which];
            preferences.edit().putString("app_language", selectedLanguage).apply();
            languageText.setText(languages[which]);
            dialog.dismiss();
            showToast(getString(R.string.language_changed));

            // Apply language change immediately
            SettingsHelper.changeLanguage(SettingsActivity.this, selectedLanguage);
        });
        builder.setNegativeButton(getString(R.string.cancel_button), null);
        builder.show();
    }

    private void showQualityDialog() {
        String[] qualities = {getString(R.string.quality_high), getString(R.string.quality_medium), getString(R.string.quality_low)};
        String[] qualityCodes = {"high", "medium", "low"};

        String currentQuality = preferences.getString("image_quality", "high");
        int selectedIndex = 0;
        for (int i = 0; i < qualityCodes.length; i++) {
            if (qualityCodes[i].equals(currentQuality)) {
                selectedIndex = i;
                break;
            }
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.image_quality));
        builder.setSingleChoiceItems(qualities, selectedIndex, (dialog, which) -> {
            String selectedQuality = qualityCodes[which];
            preferences.edit().putString("image_quality", selectedQuality).apply();
            qualityText.setText(qualities[which]);
            dialog.dismiss();
            showToast(String.format(getString(R.string.quality_changed), qualities[which]));

            // Apply quality change immediately
            applyImageQualityChange(selectedQuality);
        });
        builder.setNegativeButton(getString(R.string.cancel), null);
        builder.show();
    }

    private void applySavedLanguage() {
        try {
            String savedLanguage = preferences.getString("app_language", "ar");

            java.util.Locale locale;
            if (savedLanguage.equals("ar")) {
                locale = new java.util.Locale("ar");
            } else {
                locale = new java.util.Locale("en");
            }

            java.util.Locale.setDefault(locale);
            android.content.res.Configuration config = new android.content.res.Configuration();
            config.locale = locale;
            getBaseContext().getResources().updateConfiguration(config,
                getBaseContext().getResources().getDisplayMetrics());

        } catch (Exception e) {
            // Handle error silently
        }
    }

    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    private void applyLanguageChange(String languageCode) {
        try {
            // Save language preference
            preferences.edit().putString("app_language", languageCode).apply();

            // Apply locale change
            java.util.Locale locale;
            if (languageCode.equals("ar")) {
                locale = new java.util.Locale("ar");
            } else {
                locale = new java.util.Locale("en");
            }

            java.util.Locale.setDefault(locale);
            android.content.res.Configuration config = new android.content.res.Configuration();
            config.locale = locale;
            getBaseContext().getResources().updateConfiguration(config,
                getBaseContext().getResources().getDisplayMetrics());

            // Restart activity to apply changes
            Intent intent = getIntent();
            finish();
            startActivity(intent);

        } catch (Exception e) {
            showToast("خطأ في تغيير اللغة / Error changing language");
        }
    }

    private void applyDarkMode(boolean isDarkMode) {
        try {
            // Save dark mode preference
            preferences.edit().putBoolean("dark_mode_enabled", isDarkMode).apply();

            if (isDarkMode) {
                // Apply dark theme colors to current activity
                getWindow().getDecorView().setBackgroundColor(0xFF121212); // Dark gray
                getWindow().setStatusBarColor(0xFF000000); // Black status bar

                // Apply to toolbar if exists
                if (getSupportActionBar() != null) {
                    getSupportActionBar().setBackgroundDrawable(
                        new android.graphics.drawable.ColorDrawable(0xFF1F1F1F));
                }
            } else {
                // Apply light theme colors
                getWindow().getDecorView().setBackgroundColor(0xFFFFFFFF); // White
                getWindow().setStatusBarColor(0xFF6200EE); // Primary color

                // Apply to toolbar if exists
                if (getSupportActionBar() != null) {
                    getSupportActionBar().setBackgroundDrawable(
                        new android.graphics.drawable.ColorDrawable(0xFF6200EE));
                }
            }

            // Apply to all activities in the app
            applyDarkModeToApp(isDarkMode);

        } catch (Exception e) {
            // Handle any errors gracefully
            showToast("خطأ في تطبيق الوضع الليلي");
        }
    }

    private void applyDarkModeToApp(boolean isDarkMode) {
        // This will apply dark mode to the entire app
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                if (isDarkMode) {
                    getDelegate().setLocalNightMode(androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_YES);
                } else {
                    getDelegate().setLocalNightMode(androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_NO);
                }
            }
        } catch (Exception e) {
            // Fallback for older versions
        }
    }

    private void applyImageQualityChange(String quality) {
        try {
            // Save quality preference
            preferences.edit().putString("image_quality", quality).apply();

            // Apply quality settings globally
            SharedPreferences globalPrefs = getSharedPreferences("app_settings", Context.MODE_PRIVATE);
            globalPrefs.edit().putString("image_quality", quality).apply();

            // Set quality parameters based on selection
            int compressionQuality;
            int maxImageSize;

            switch (quality) {
                case "high":
                    compressionQuality = 95;
                    maxImageSize = 2048;
                    break;
                case "medium":
                    compressionQuality = 80;
                    maxImageSize = 1024;
                    break;
                case "low":
                    compressionQuality = 60;
                    maxImageSize = 512;
                    break;
                default:
                    compressionQuality = 95;
                    maxImageSize = 2048;
                    break;
            }

            // Save quality parameters
            globalPrefs.edit()
                .putInt("compression_quality", compressionQuality)
                .putInt("max_image_size", maxImageSize)
                .apply();

            showToast(getString(R.string.quality_applied));

        } catch (Exception e) {
            showToast(getString(R.string.quality_error));
        }
    }

    private String getQualityText(String quality) {
        switch (quality) {
            case "high": return getString(R.string.quality_high);
            case "medium": return getString(R.string.quality_medium);
            case "low": return getString(R.string.quality_low);
            default: return getString(R.string.quality_high);
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected boolean useToolbar() {
        return true;
    }

    @Override
    protected boolean useDrawerToggle() {
        return false;
    }
}