<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Dark theme styles -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/background</item>
        <item name="android:textColorPrimary">@color/textPrimary</item>
        <item name="android:textColorSecondary">@color/textSecondary</item>
        <item name="android:statusBarColor">@color/status_bar_color</item>
        <item name="android:navigationBarColor">@color/navigation_bar_color</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/background</item>
        <item name="android:statusBarColor">@color/status_bar_color</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="android:background">@color/surface</item>
    </style>

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Dark">
        <item name="android:background">@color/surface</item>
    </style>

    <!-- Premium Card Styles for dark theme -->
    <style name="PremiumCardStyle">
        <item name="cardCornerRadius">24dp</item>
        <item name="cardElevation">16dp</item>
        <item name="cardUseCompatPadding">true</item>
        <item name="cardBackgroundColor">@color/modern_card_bg</item>
    </style>

    <!-- Premium Text Styles for dark theme -->
    <style name="PremiumTitleText">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:fontFamily">@font/blabeloo</item>
        <item name="android:shadowColor">#80000000</item>
        <item name="android:shadowDx">2</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">4</item>
        <item name="android:letterSpacing">0.05</item>
    </style>

    <style name="PremiumSubtitleText">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/textSecondary</item>
        <item name="android:fontFamily">@font/blabeloo</item>
        <item name="android:alpha">0.9</item>
    </style>

    <!-- Premium Button Styles for dark theme -->
    <style name="PremiumButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/premium_button_background</item>
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/blabeloo</item>
        <item name="android:elevation">8dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <!-- Modern Switch Style for dark theme -->
    <style name="ModernSwitchStyle">
        <item name="colorAccent">@color/modern_primary</item>
        <item name="colorControlActivated">@color/modern_primary</item>
        <item name="colorSwitchThumbNormal">@color/text_secondary</item>
        <item name="android:colorForeground">@color/modern_primary</item>
    </style>
</resources>
