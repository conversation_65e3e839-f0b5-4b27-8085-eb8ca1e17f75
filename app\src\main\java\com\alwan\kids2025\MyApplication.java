package com.alwan.kids2025;

import android.app.Application;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.preference.PreferenceManager;
import java.util.Locale;

public class MyApplication extends Application {
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        // Apply saved language and theme settings
        applySavedSettings();
    }
    
    private void applySavedSettings() {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(this);
        
        // Apply saved language
        String savedLanguage = preferences.getString("app_language", "ar");
        applyLanguage(savedLanguage);
        
        // Apply saved dark mode
        boolean isDarkMode = preferences.getBoolean("dark_mode_enabled", false);
        applyDarkMode(isDarkMode);
    }
    
    private void applyLanguage(String languageCode) {
        try {
            Locale locale;
            if (languageCode.equals("ar")) {
                locale = new Locale("ar");
            } else {
                locale = new Locale("en");
            }
            
            Locale.setDefault(locale);
            Configuration config = new Configuration();
            config.locale = locale;
            getBaseContext().getResources().updateConfiguration(config,
                getBaseContext().getResources().getDisplayMetrics());
                
        } catch (Exception e) {
            // Handle error silently
        }
    }
    
    private void applyDarkMode(boolean isDarkMode) {
        try {
            if (isDarkMode) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
            }
        } catch (Exception e) {
            // Handle error silently
        }
    }
}
