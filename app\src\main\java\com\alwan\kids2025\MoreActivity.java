package com.alwan.kids2025;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.widget.LinearLayout;

public class MoreActivity extends BaseActivity {

    private LinearLayout moreSettings;
    private LinearLayout moreAbout;
    private LinearLayout moreShare;
    private LinearLayout moreRate;
    private LinearLayout morePrivacy;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_more);

        // Initialize views
        initViews();

        // Setup listeners
        setupListeners();

        // Setup bottom navigation
        setupBottomNavigation();
    }

    private void initViews() {
        moreSettings = findViewById(R.id.more_settings);
        moreAbout = findViewById(R.id.more_about);
        moreShare = findViewById(R.id.more_share);
        moreRate = findViewById(R.id.more_rate);
        morePrivacy = findViewById(R.id.more_privacy);
    }

    private void setupListeners() {
        moreSettings.setOnClickListener(v -> {
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
        });

        moreAbout.setOnClickListener(v -> {
            Intent intent = new Intent(this, AboutActivity.class);
            startActivity(intent);
        });

        moreShare.setOnClickListener(v -> {
            shareTextUrl();
        });

        moreRate.setOnClickListener(v -> {
            String packageName = getPackageName();
            Intent intent = new Intent(Intent.ACTION_VIEW,
                Uri.parse("https://play.google.com/store/apps/details?id=" + packageName));
            startActivity(intent);
        });

        morePrivacy.setOnClickListener(v -> {
            Intent intent = new Intent(Intent.ACTION_VIEW,
                Uri.parse("https://sites.google.com/view/colors-kids"));
            startActivity(intent);
        });
    }

    private void setupBottomNavigation() {
        LinearLayout navHome = findViewById(R.id.nav_home);
        LinearLayout navCategories = findViewById(R.id.nav_categories);
        LinearLayout navGallery = findViewById(R.id.nav_gallery);
        LinearLayout navFavorites = findViewById(R.id.nav_favorites);
        LinearLayout navMore = findViewById(R.id.nav_more);

        // Set current tab as active (More)
        setActiveTab(navMore);

        navHome.setOnClickListener(v -> {
            Intent intent = new Intent(this, Categories.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        });

        navCategories.setOnClickListener(v -> {
            Intent intent = new Intent(this, Categories.class);
            startActivity(intent);
            finish();
        });

        navGallery.setOnClickListener(v -> {
            Intent intent = new Intent(this, GalleryActivity.class);
            startActivity(intent);
            finish();
        });

        navFavorites.setOnClickListener(v -> {
            Intent intent = new Intent(this, FavoritesActivity.class);
            startActivity(intent);
            finish();
        });

        navMore.setOnClickListener(v -> {
            // Already on More page
        });
    }

    private void setActiveTab(LinearLayout activeTab) {
        // Reset all tabs
        resetTab(findViewById(R.id.nav_home));
        resetTab(findViewById(R.id.nav_categories));
        resetTab(findViewById(R.id.nav_gallery));
        resetTab(findViewById(R.id.nav_favorites));
        resetTab(findViewById(R.id.nav_more));

        // Set active tab
        if (activeTab != null) {
            activeTab.findViewById(R.id.nav_more_icon).setBackgroundColor(
                getResources().getColor(R.color.modern_primary));
            ((android.widget.TextView) activeTab.findViewById(R.id.nav_more_text))
                .setTextColor(getResources().getColor(R.color.modern_primary));
            ((android.widget.TextView) activeTab.findViewById(R.id.nav_more_text))
                .setTypeface(null, android.graphics.Typeface.BOLD);
        }
    }

    private void resetTab(LinearLayout tab) {
        if (tab != null) {
            // Reset icon background
            if (tab.getId() == R.id.nav_home) {
                tab.findViewById(R.id.nav_home_icon).setBackgroundColor(
                    getResources().getColor(R.color.text_secondary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_home_text))
                    .setTextColor(getResources().getColor(R.color.text_primary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_home_text))
                    .setTypeface(null, android.graphics.Typeface.NORMAL);
            } else if (tab.getId() == R.id.nav_categories) {
                tab.findViewById(R.id.nav_categories_icon).setBackgroundColor(
                    getResources().getColor(R.color.text_secondary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_categories_text))
                    .setTextColor(getResources().getColor(R.color.text_primary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_categories_text))
                    .setTypeface(null, android.graphics.Typeface.NORMAL);
            } else if (tab.getId() == R.id.nav_gallery) {
                tab.findViewById(R.id.nav_gallery_icon).setBackgroundColor(
                    getResources().getColor(R.color.text_secondary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_gallery_text))
                    .setTextColor(getResources().getColor(R.color.text_primary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_gallery_text))
                    .setTypeface(null, android.graphics.Typeface.NORMAL);
            } else if (tab.getId() == R.id.nav_favorites) {
                tab.findViewById(R.id.nav_favorites_icon).setBackgroundColor(
                    getResources().getColor(R.color.text_secondary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_favorites_text))
                    .setTextColor(getResources().getColor(R.color.text_primary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_favorites_text))
                    .setTypeface(null, android.graphics.Typeface.NORMAL);
            } else if (tab.getId() == R.id.nav_more) {
                tab.findViewById(R.id.nav_more_icon).setBackgroundColor(
                    getResources().getColor(R.color.text_secondary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_more_text))
                    .setTextColor(getResources().getColor(R.color.text_primary));
                ((android.widget.TextView) tab.findViewById(R.id.nav_more_text))
                    .setTypeface(null, android.graphics.Typeface.NORMAL);
            }
        }
    }

    private void shareTextUrl() {
        String packageName = getPackageName();
        Intent share = new Intent(Intent.ACTION_SEND);
        share.setType("text/plain");
        share.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        share.putExtra(Intent.EXTRA_TEXT, String.format(getString(R.string.share_app_text), packageName));
        startActivity(Intent.createChooser(share, getString(R.string.share_app_title)));
    }

    @Override
    protected boolean useToolbar() {
        return false;
    }

    @Override
    protected boolean useDrawerToggle() {
        return false;
    }
}
