{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeDebugResources-56:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\952446e2a2fa218bc85ba4e6563abcfd\\transformed\\jetified-play-services-ads-22.0.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "214,215,216,229,230,231,232,233,234,235,248,249,250,251,252,253,254,276", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16317,16362,16414,17192,17264,17368,17425,17543,17604,17720,18559,18601,18684,18720,18755,18802,18874,20544", "endColumns": "44,51,57,71,103,56,117,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "16357,16409,16467,17259,17363,17420,17538,17599,17715,17804,18596,18679,18715,18750,18797,18869,18913,20599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1a8cebdf235588805d6f74854d945d7\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "112,129,224,239,270,273,274", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8649,9944,16826,17975,20052,20319,20401", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "8711,10032,16907,18103,20216,20396,20476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4630b1237475cc69c83d8707fc0d9479\\transformed\\browser-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "113,131,132,133", "startColumns": "4,4,4,4", "startOffsets": "8716,10128,10226,10334", "endColumns": "99,97,107,101", "endOffsets": "8811,10221,10329,10431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cbd131f1b1e0c9680dc8c1d103e011d9\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "7507", "endColumns": "129", "endOffsets": "7632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,72,73,74,75,76,89,90,93,120,121,130,147,149,154,155,156,157,158,159,160,161,162,163,164,165,166,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,237,257,258,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4766,4844,4920,5004,5096,6132,6233,6476,9202,9261,10037,11094,11216,11567,11667,11730,11795,11856,11924,11986,12044,12158,12218,12279,12336,12409,12618,12699,12791,12898,12996,13076,13224,13305,13386,13514,13603,13679,13732,13786,13852,13930,14010,14081,14163,14235,14309,14382,14452,14561,14652,14723,14813,14908,14982,15065,15158,15207,15288,15357,15443,15528,15590,15654,15717,15786,15895,16005,16102,16202,16259,17846,19047,19126,19580", "endLines": "9,72,73,74,75,76,89,90,93,120,121,130,147,149,154,155,156,157,158,159,160,161,162,163,164,165,166,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,237,257,258,263", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,4839,4915,4999,5091,5174,6228,6347,6548,9256,9319,10123,11158,11278,11662,11725,11790,11851,11919,11981,12039,12153,12213,12274,12331,12404,12527,12694,12786,12893,12991,13071,13219,13300,13381,13509,13598,13674,13727,13781,13847,13925,14005,14076,14158,14230,14304,14377,14447,14556,14647,14718,14808,14903,14977,15060,15153,15202,15283,15352,15438,15523,15585,15649,15712,15781,15890,16000,16097,16197,16254,16312,17921,19121,19196,19651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1273d61679a02c43f1de22c3f405d6f\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "77,78,79,80,81,82,83,269", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5179,5272,5374,5469,5572,5675,5777,19951", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "5267,5369,5464,5567,5670,5772,5886,20047"}}, {"source": "Z:\\alwan5\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,139,-1,-1,-1,145,-1,-1,-1,-1,-1,-1,-1,-1,138,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,137,-1,140,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,142,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6497,-1,-1,-1,6741,-1,-1,-1,-1,-1,-1,-1,-1,6442,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6389,-1,6553,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6661,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6604,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,54,-1,-1,-1,57,-1,-1,-1,-1,-1,-1,-1,-1,53,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,49,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,51,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,55,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6547,-1,-1,-1,6794,-1,-1,-1,-1,-1,-1,-1,-1,6491,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6436,-1,6598,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6708,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6655,-1,-1"}, "to": {"startLines": "10,38,39,40,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,84,85,86,87,88,91,92,114,115,116,117,118,119,122,123,124,125,126,127,128,134,135,136,137,138,139,140,141,142,143,144,145,146,148,150,151,152,153,167,168,217,218,219,220,221,222,223,225,226,227,228,236,238,240,241,242,243,244,245,246,247,255,259,260,261,262,264,265,266,267,268,271,272,275,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3224,3262,3312,3816,3866,3930,3974,4022,4068,4114,4158,4213,4274,4324,4372,4430,4482,4553,4623,4695,5891,5932,5989,6032,6086,6352,6403,8816,8885,8953,9011,9102,9149,9324,9427,9498,9606,9731,9813,9885,10436,10482,10534,10575,10627,10665,10715,10758,10813,10883,10937,10995,11052,11163,11283,11381,11438,11525,12532,12572,16472,16513,16565,16625,16686,16720,16771,16912,16973,17031,17111,17809,17926,18108,18160,18234,18306,18376,18423,18470,18520,18918,19201,19248,19301,19521,19656,19707,19775,19842,19897,20221,20263,20481,20604", "endLines": "10,38,39,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,84,85,86,87,88,91,92,114,115,116,117,118,119,122,123,124,125,126,127,128,134,135,136,137,138,139,140,141,142,143,144,145,146,148,150,151,152,153,167,168,217,218,219,220,221,222,223,225,226,227,228,236,238,240,241,242,243,244,245,246,247,255,259,260,261,262,264,265,266,267,268,271,272,275,277", "endColumns": "54,37,49,59,49,63,43,47,45,45,43,54,60,49,47,57,51,70,69,71,70,40,56,42,53,45,50,72,68,67,57,90,46,52,102,70,107,124,81,71,58,45,51,40,51,37,49,42,54,69,53,57,56,41,52,97,56,86,41,39,45,40,51,59,60,33,50,54,60,57,79,80,36,48,51,73,71,69,46,46,49,38,46,46,52,219,58,50,67,66,54,53,41,55,62,35", "endOffsets": "565,3257,3307,3811,3861,3925,3969,4017,4063,4109,4153,4208,4269,4319,4367,4425,4477,4548,4618,4690,4761,5927,5984,6027,6081,6127,6398,6471,8880,8948,9006,9097,9144,9197,9422,9493,9601,9726,9808,9880,9939,10477,10529,10570,10622,10660,10710,10753,10808,10878,10932,10990,11047,11089,11211,11376,11433,11520,11562,12567,12613,16508,16560,16620,16681,16715,16766,16821,16968,17026,17106,17187,17841,17970,18155,18229,18301,18371,18418,18465,18515,18554,18960,19243,19296,19516,19575,19702,19770,19837,19892,19946,20258,20314,20539,20635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "570,678,782,889,971,1072,1186,1266,1345,1436,1529,1621,1715,1815,1908,2003,2096,2187,2281,2360,2465,2563,2661,2769,2869,2972,3127,18965", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "673,777,884,966,1067,1181,1261,1340,1431,1524,1616,1710,1810,1903,1998,2091,2182,2276,2355,2460,2558,2656,2764,2864,2967,3122,3219,19042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a28de868e92df6a446272321a9f2ec8d\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ar\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "94,95,96,97,98,99,100,101,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6553,6657,6801,6923,7028,7166,7294,7405,7637,7774,7878,8028,8150,8289,8435,8499,8565", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "6652,6796,6918,7023,7161,7289,7400,7502,7769,7873,8023,8145,8284,8430,8494,8560,8644"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.alwan.kids2025.app-mergeDebugResources-56:\\values-ar\\values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\952446e2a2fa218bc85ba4e6563abcfd\\transformed\\jetified-play-services-ads-22.0.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,288,342,410,510,563,677,734,846,931,969,1048,1080,1111,1154,1222,1262", "endColumns": "40,47,53,67,99,52,113,56,111,84,37,78,31,30,42,67,39,55", "endOffsets": "239,287,341,409,509,562,676,733,845,930,968,1047,1079,1110,1153,1221,1261,1317"}, "to": {"startLines": "209,210,211,223,224,225,226,227,228,229,242,243,244,245,246,247,248,269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16048,16093,16145,16871,16943,17047,17104,17222,17283,17399,18238,18280,18363,18399,18434,18481,18553,20167", "endColumns": "44,51,57,71,103,56,117,60,115,88,41,82,35,34,46,71,43,59", "endOffsets": "16088,16140,16198,16938,17042,17099,17217,17278,17394,17483,18275,18358,18394,18429,18476,18548,18592,20222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1a8cebdf235588805d6f74854d945d7\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "109,126,218,233,264,266,267", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8482,9777,16505,17654,19731,19942,20024", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "8544,9865,16586,17782,19895,20019,20099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4630b1237475cc69c83d8707fc0d9479\\transformed\\browser-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "110,128,129,130", "startColumns": "4,4,4,4", "startOffsets": "8549,9961,10059,10167", "endColumns": "99,97,107,101", "endOffsets": "8644,10054,10162,10264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cbd131f1b1e0c9680dc8c1d103e011d9\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "99", "startColumns": "4", "startOffsets": "7340", "endColumns": "129", "endOffsets": "7465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\36dc858f18a2a1e2b92bb4036b7b9aba\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "2,70,71,72,73,74,86,87,90,117,118,127,142,144,149,150,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,231,251,252,257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,4653,4731,4807,4891,4983,5965,6066,6309,9035,9094,9870,10825,10947,11298,11398,11461,11526,11587,11655,11717,11775,11889,11949,12010,12067,12140,12349,12430,12522,12629,12727,12807,12955,13036,13117,13245,13334,13410,13463,13517,13583,13661,13741,13812,13894,13966,14040,14113,14183,14292,14383,14454,14544,14639,14713,14796,14889,14938,15019,15088,15174,15259,15321,15385,15448,15517,15626,15736,15833,15933,15990,17525,18726,18805,19259", "endLines": "9,70,71,72,73,74,86,87,90,117,118,127,142,144,149,150,151,152,153,154,155,156,157,158,159,160,161,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,231,251,252,257", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "510,4726,4802,4886,4978,5061,6061,6180,6381,9089,9152,9956,10889,11009,11393,11456,11521,11582,11650,11712,11770,11884,11944,12005,12062,12135,12258,12425,12517,12624,12722,12802,12950,13031,13112,13240,13329,13405,13458,13512,13578,13656,13736,13807,13889,13961,14035,14108,14178,14287,14378,14449,14539,14634,14708,14791,14884,14933,15014,15083,15169,15254,15316,15380,15443,15512,15621,15731,15828,15928,15985,16043,17600,18800,18875,19330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d1273d61679a02c43f1de22c3f405d6f\\transformed\\core-1.13.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "75,76,77,78,79,80,81,263", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5066,5159,5261,5356,5459,5562,5664,19630", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "5154,5256,5351,5454,5557,5659,5773,19726"}}, {"source": "Z:\\alwan5\\app\\src\\main\\res\\values-ar\\strings.xml", "from": {"startLines": "6,10,99,14,54,51,3,2,57,42,34,118,1,121,69,85,84,83,82,13,73,32,125,59,71,77,76,101,102,45,70,52,50,48,47,49,132,131,127,108,31,33,126,105,43,46,87,86,124,90,72,68,53,9,128,111,35,5,4,134,44,58,62,61,64,63,12,8,39,95,94,96,91,93,92,11,7,41,67,114,115,40,81,80,56,55,36,100,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,482,4888,641,2569,2312,117,68,2731,1649,1312,5725,17,5814,3324,4219,4148,4075,4003,599,3604,1229,5939,2834,3431,3765,3696,5003,5062,1819,3377,2377,2240,2048,1922,2157,6206,6146,6030,5260,1187,1273,5986,5178,1696,1867,4349,4291,5896,4441,3505,3266,2481,439,6077,5339,1357,224,162,6316,1767,2778,2947,2888,3091,3009,561,389,1496,4715,4642,4790,4495,4594,4543,521,341,1601,3212,5413,5634,1549,3934,3866,2675,2620,1399,4939,6279", "endLines": "6,10,99,28,54,51,3,2,57,42,34,118,1,121,69,85,84,83,82,13,73,32,125,59,71,77,76,101,102,45,70,52,50,48,47,49,132,131,127,108,31,33,126,105,43,46,87,86,124,90,72,68,53,9,128,111,35,5,4,134,44,58,62,61,64,63,12,8,39,95,94,96,91,93,92,11,7,41,67,114,115,40,81,80,56,55,36,100,133", "endColumns": "54,37,49,59,49,63,43,47,45,45,43,60,49,47,51,70,69,71,70,40,56,42,45,50,72,68,67,57,90,46,52,102,70,107,124,81,71,58,45,51,40,37,42,54,69,53,57,56,41,52,97,56,86,41,39,45,40,59,60,33,50,54,60,57,79,80,36,48,51,73,71,69,46,46,49,38,46,46,52,219,58,50,67,66,54,53,41,62,35", "endOffsets": "335,515,4933,1154,2614,2371,156,111,2772,1690,1351,5781,62,5857,3371,4285,4213,4142,4069,635,3656,1267,5980,2880,3499,3829,3759,5056,5148,1861,3425,2475,2306,2151,2042,2234,6273,6200,6071,5307,1223,1306,6024,5228,1761,1916,4402,4343,5933,4489,3598,3318,2563,476,6112,5380,1393,279,218,6345,1813,2828,3003,2941,3166,3085,593,433,1543,4784,4709,4855,4537,4636,4588,555,383,1643,3260,5628,5688,1595,3997,3928,2725,2669,1436,4997,6310"}, "to": {"startLines": "10,38,39,40,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,82,83,84,85,88,89,111,112,113,114,115,116,119,120,121,122,123,124,125,131,132,133,134,135,136,137,138,139,140,141,143,145,146,147,148,162,163,212,213,214,215,216,217,219,220,221,222,230,232,234,235,236,237,238,239,240,241,249,253,254,255,256,258,259,260,261,262,265,268,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,3224,3262,3312,3816,3866,3930,3974,4022,4068,4114,4158,4219,4269,4317,4369,4440,4510,4582,5778,5819,5876,5919,6185,6236,8649,8718,8786,8844,8935,8982,9157,9260,9331,9439,9564,9646,9718,10269,10315,10367,10408,10446,10489,10544,10614,10668,10726,10783,10894,11014,11112,11169,11256,12263,12303,16203,16244,16304,16365,16399,16450,16591,16652,16710,16790,17488,17605,17787,17839,17913,17985,18055,18102,18149,18199,18597,18880,18927,18980,19200,19335,19386,19454,19521,19576,19900,20104,20227", "endLines": "10,38,39,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,82,83,84,85,88,89,111,112,113,114,115,116,119,120,121,122,123,124,125,131,132,133,134,135,136,137,138,139,140,141,143,145,146,147,148,162,163,212,213,214,215,216,217,219,220,221,222,230,232,234,235,236,237,238,239,240,241,249,253,254,255,256,258,259,260,261,262,265,268,270", "endColumns": "54,37,49,59,49,63,43,47,45,45,43,60,49,47,51,70,69,71,70,40,56,42,45,50,72,68,67,57,90,46,52,102,70,107,124,81,71,58,45,51,40,37,42,54,69,53,57,56,41,52,97,56,86,41,39,45,40,59,60,33,50,54,60,57,79,80,36,48,51,73,71,69,46,46,49,38,46,46,52,219,58,50,67,66,54,53,41,62,35", "endOffsets": "565,3257,3307,3811,3861,3925,3969,4017,4063,4109,4153,4214,4264,4312,4364,4435,4505,4577,4648,5814,5871,5914,5960,6231,6304,8713,8781,8839,8930,8977,9030,9255,9326,9434,9559,9641,9713,9772,10310,10362,10403,10441,10484,10539,10609,10663,10721,10778,10820,10942,11107,11164,11251,11293,12298,12344,16239,16299,16360,16394,16445,16500,16647,16705,16785,16866,17520,17649,17834,17908,17980,18050,18097,18144,18194,18233,18639,18922,18975,19195,19254,19381,19449,19516,19571,19625,19937,20162,20258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40acce1c3a2b617941018f107a306df6\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "570,678,782,889,971,1072,1186,1266,1345,1436,1529,1621,1715,1815,1908,2003,2096,2187,2281,2360,2465,2563,2661,2769,2869,2972,3127,18644", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "673,777,884,966,1067,1181,1261,1340,1431,1524,1616,1710,1810,1903,1998,2091,2182,2276,2355,2460,2558,2656,2764,2864,2967,3122,3219,18721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a28de868e92df6a446272321a9f2ec8d\\transformed\\jetified-play-services-base-18.0.1\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "91,92,93,94,95,96,97,98,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6386,6490,6634,6756,6861,6999,7127,7238,7470,7607,7711,7861,7983,8122,8268,8332,8398", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "6485,6629,6751,6856,6994,7122,7233,7335,7602,7706,7856,7978,8117,8263,8327,8393,8477"}}]}]}